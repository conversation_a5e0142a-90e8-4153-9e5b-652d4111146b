#generate_video.py
import os
import json
import argparse
import logging
import subprocess
import tempfile
import shutil
import time
from typing import List, Dict, Any, Tuple, Optional
from collections import OrderedDict
from decimal import Decimal

from images2video import generate_video_from_image_with_depthflow
from image2clip import generate_video_from_image, generate_video_from_images
from audio2clip import generate_video_from_audio
from config import (
    CLIP_DIR, logger, VIDEO_RESOLUTION, VIDEO_BITRATE, VIDEO_FPS, VIDEO_PRESET,
    VIDEO_CRF, VIDEO_CODEC, VIDEO_AUDIO_CODEC, FFMPEG_BASE_PARAMS,
    VIDEO_ENCODE_PARAMS, AUDIO_ENCODE_PARAMS, TEMP_FILE_DIR, TEMP_DIR,
    XFADE_TRANSITION_TIME, RESIZED_IMAGE_DIR
)
from modules.nlp_model import get_language_code
from modules.utils import resize_large_image, upscale_image

# 日志初始化
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO) # Explicitly set level for this logger

GENERATE_VIDEO_DEBUG = False  # 新增调试变量
CLIP_BATCH_SIZE = 8  # 新增：每批处理的视频片段数量
DEFAULT_TRANSITION_TYPE = "fade"  # 新增：默认转场类型
# ============== 工具函数 ==============

def load_json_file(file_path: str) -> List[Dict[str, Any]]:
    """加载并验证JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            segments = json.load(f)

        if not isinstance(segments, list):
            raise ValueError("JSON文件必须包含一个列表")

        # 验证并排序 segment_id
        for i, seg in enumerate(segments):
            if 'segment_id' not in seg:
                logger.warning(f"段落 {i+1} 缺少segment_id，将使用索引作为ID")
                seg['segment_id'] = i + 1
        segments.sort(key=lambda x: x['segment_id'])

        # 检查重复
        ids = [s['segment_id'] for s in segments]
        if len(ids) != len(set(ids)):
            raise ValueError("JSON文件内存在重复的segment_id")

        logger.info(f"成功加载 {len(segments)} 个段落")
        return segments
    except Exception as e:
        logger.error(f"加载JSON文件失败 {file_path}: {e}")
        raise

def get_video_duration(file_path: str) -> Decimal:
    """获取视频文件的精确时长（单位:秒）"""
    try:
        cmd = [
            'ffprobe',
            '-v', 'error',
            '-show_entries', 'format=duration',
            '-of', 'default=noprint_wrappers=1:nokey=1',
            file_path
        ]
        duration = subprocess.check_output(cmd).decode().strip()
        return Decimal(duration)
    except Exception as e:
        logger.error(f"获取视频时长失败 {file_path}: {e}")
        raise

def format_float(value: Decimal, precision: int = 6) -> str:
    """将Decimal值格式化为指定小数位"""
    return format(float(value), f'.{precision}f')

def calculate_safe_offset(clip_duration: Decimal, transition_duration: Decimal) -> Tuple[Decimal, Decimal]:
    """
    计算安全的过渡参数

    Args:
        clip_duration: 当前片段的时长
        transition_duration: 期望的过渡时长

    Returns:
        Tuple[Decimal, Decimal]: (片段内的偏移值, 实际过渡时长)
    """
    # 确保过渡时长合理
    actual_trans = min(clip_duration, transition_duration)
    actual_trans = max(Decimal('0'), actual_trans)

    # 过渡开始点是当前片段时长减去过渡时长
    offset_in_clip = clip_duration - actual_trans  # 修改：从加法改为减法

    return offset_in_clip, actual_trans

def verify_media_file(file_path: str, require_video: bool = True, require_audio: bool = True) -> bool:
    """验证媒体文件是否包含所需的视频和音频流

    Args:
        file_path: 要验证的文件路径
        require_video: 是否要求包含视频流
        require_audio: 是否要求包含音频流

    Returns:
        bool: 文件是否有效

    Raises:
        ValueError: 如果文件不符合要求
    """
    if not os.path.exists(file_path):
        raise ValueError(f"文件不存在: {file_path}")
    if os.path.getsize(file_path) == 0:
        raise ValueError(f"文件为空: {file_path}")

    try:
        cmd = ["ffprobe", "-v", "error", "-show_entries",
               "stream=codec_type", "-of", "json", file_path]
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        info = json.loads(result.stdout)

        streams = info.get('streams', [])
        has_video = any(stream.get('codec_type') == 'video' for stream in streams)
        has_audio = any(stream.get('codec_type') == 'audio' for stream in streams)

        missing = []
        if require_video and not has_video:
            missing.append("视频流")
        if require_audio and not has_audio:
            missing.append("音频流")

        if missing:
            raise ValueError(f"文件缺少{', '.join(missing)}: {file_path}")

        return True
    except subprocess.CalledProcessError as e:
        raise ValueError(f"无法检查文件流信息: {file_path}, 错误: {e}")
    except json.JSONDecodeError as e:
        raise ValueError(f"无法解析文件流信息: {file_path}, 错误: {e}")

# ============== 处理单个 Segment ==============

def process_segment(
    segment: Dict[str, Any],
    output_dir: str,
    resolution: tuple = VIDEO_RESOLUTION,
    fps: int = VIDEO_FPS,
    segment_index: int = None,
    lang_code: str = 'en',
    use_depthflow: bool = False,  # 是否使用 DepthFlow
    use_crop: bool = False,       # 是否使用 cover+crop 预处理
    use_upscale: bool = False     # 是否对图片进行4x放大处理
) -> str:
    """处理单个段落，生成独立视频片段

    Args:
        segment: 段落数据
        output_dir: 输出目录
        resolution: 视频分辨率
        fps: 视频帧率
        segment_index: 段落索引
        lang_code: 语言代码
        use_depthflow: 是否使用 DepthFlow 效果
        use_crop: 是否使用 cover+crop 预处理
        use_upscale: 是否对图片进行4x放大处理

    Returns:
        str: 输出文件路径
    """
    try:
        # 生成输出文件名
        output_name = f"clip_{segment_index:04d}.mp4" if segment_index is not None else f"clip_{segment.get('segment_id', 0):04d}.mp4"
        output_path = os.path.join(output_dir, output_name)

        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # 跳过已经生成的片段
        if not GENERATE_VIDEO_DEBUG and os.path.exists(output_path) and os.path.getsize(output_path) > 0:
            logger.info(f"段落 {segment_index}: 片段已存在，跳过生成: {output_path}")
            return output_path
        elif GENERATE_VIDEO_DEBUG and os.path.exists(output_path) and os.path.getsize(output_path) > 0:
            logger.info(f"段落 {segment_index}: DEBUG模式下重新生成已存在片段: {output_path}")
            try:
                os.remove(output_path)
            except OSError as e:
                logger.warning(f"无法删除已存在的文件 {output_path}: {e}")

        # 获取基本信息
        audio_info = segment.get("audio", {})
        audio_path = audio_info.get("path", None)
        allocated_duration = float(audio_info.get("duration", 0.0))

        # 获取实际音频时长，确保不超过音频内容长度
        if audio_path and os.path.exists(audio_path):
            try:
                actual_audio_duration = float(get_video_duration(audio_path))
                # 使用实际音频时长和分配时长的较小值
                duration = min(allocated_duration, actual_audio_duration)

                if allocated_duration > actual_audio_duration:
                    logger.info(f"段落 {segment_index}: 分配时长({allocated_duration:.2f}s) > 实际音频时长({actual_audio_duration:.2f}s)，使用音频时长")
                else:
                    logger.debug(f"段落 {segment_index}: 使用分配时长({duration:.2f}s)")
            except Exception as e:
                logger.warning(f"段落 {segment_index}: 无法获取音频时长，使用分配时长: {e}")
                duration = allocated_duration
        else:
            logger.warning(f"段落 {segment_index}: 音频文件不存在，使用分配时长")
            duration = allocated_duration

        # 获取媒体信息
        media_info = segment.get("assigned_media", {})
        media_type = media_info.get("type")
        media_path = media_info.get("filepath")

        # 输出调试信息，帮助排查问题
        logger.debug(f"段落 {segment_index} 媒体信息: type={media_type}, path={media_path}")

        # 获取段落文本内容，用于语义分割
        paragraph_text = segment.get("paragraph_text", "")

        # 检查是否有预设配置，用于 DepthFlow
        depthflow_preset = media_info.get("depthflow_preset", None)

        # 从output_dir推断theme
        theme = os.path.basename(os.path.dirname(os.path.dirname(output_path)))
        logger.debug(f"段落 {segment_index}: 从输出路径推断主题: {theme}")

        # 改进的媒体类型判断逻辑
        if media_type == "multi_image" and "filepaths" in media_info and media_info.get("filepaths"):
            # 有效的多图模式 - 确保filepaths非空
            image_paths = media_info.get("filepaths", [])

            # 如果需要放大，预处理所有图片
            if use_upscale:
                logger.info(f"段落 {segment_index}: 对 {len(image_paths)} 张图片进行放大处理")
                upscaled_paths = []
                for img_path in image_paths:
                    upscaled_path = upscale_image(img_path, theme)
                    upscaled_paths.append(upscaled_path)
                image_paths = upscaled_paths

            logger.info(f"段落 {segment_index}: 使用多图片生成视频，共 {len(image_paths)} 张图片")
            generate_video_from_images(
                image_paths=image_paths,
                output_path=output_path,
                audio_path=audio_path,
                duration=duration,
                text=paragraph_text,  # 传递段落文本以便语义分割
                fps=fps,
                resolution=resolution,
                crossfade=0.8,  # 使用默认的0.8秒转场
                use_crop=use_crop    # ← 新增
            )
        elif media_type == "image" and media_path and os.path.exists(media_path):
            # 有效的图片模式 - 确保文件存在

            # 如果需要放大，先处理图片
            if use_upscale:
                logger.info(f"段落 {segment_index}: 对图片进行放大处理")
                media_path = upscale_image(media_path, theme)

            if use_depthflow:
                logger.info(f"段落 {segment_index}：使用图片生成视频，应用 DepthFlow")
                generate_video_from_image_with_depthflow(
                    image_path=media_path,
                    output_path=output_path,
                    duration=duration,
                    fps=fps,
                    resolution=resolution,
                    audio_path=audio_path,
                    motion=depthflow_preset,  # 传递 DepthFlow 预设
                    image_index=segment_index
                )
            else:
                logger.info(f"段落 {segment_index}：使用图片生成视频，应用传统 Pan/Zoom 效果")
                generate_video_from_image(
                    image_path=media_path,
                    output_path=output_path,
                    duration=duration,
                    fps=fps,
                    resolution=resolution,
                    audio_path=audio_path,
                    use_crop=use_crop    # ← 新增
                )
        elif media_type == "clip" and media_path and os.path.exists(media_path):
            # 有效的视频片段模式 - 确保文件存在
            logger.info(f"段落 {segment_index}：使用视频片段生成视频")
            generate_video_from_clip(
                video_path=media_path,
                output_path=output_path,
                audio_path=audio_path,
                start_time=media_info.get("start_time"),
                end_time=media_info.get("end_time"),
                resolution=resolution,
                fps=fps
            )
        elif media_type == "anchor":
            # 明确的anchor类型 - 使用主播模式
            logger.info(f"段落 {segment_index}：使用主播模式生成视频")
            generate_video_from_audio(
                audio_path=audio_path,
                output_path=output_path,
                resolution=resolution,
                using_cache=False,
                fps=fps,
                lang_code=lang_code
            )
        else:
            # 未知或不支持的媒体类型，或缺少必要信息
            logger.debug(f"段落 {segment_index} 详细信息: {json.dumps(media_info, ensure_ascii=False)}")
            raise ValueError(f"段落 {segment_index}：未识别的媒体类型 '{media_type}'")
        # 验证生成的文件是否有效
        verify_media_file(output_path)
        return output_path
    except Exception as e:
        logger.error(f"处理段落 {segment_index} 出错: {e}")
        # 如果生成失败，删除可能部分生成的文件
        if os.path.exists(output_path):
            try:
                os.remove(output_path)
                logger.info(f"已删除无效的输出文件: {output_path}")
            except:
                pass
        raise

# ============== 合并多个视频片段 ==============

class VideoDurationCache:
    """视频时长缓存，避免重复 ffprobe 调用"""
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._cache = {}
        return cls._instance

    def get_duration(self, file_path: str) -> Decimal:
        if file_path not in self._cache:
            self._cache[file_path] = get_video_duration(file_path)
        return self._cache[file_path]

    def clear(self):
        self._cache.clear()


def merge_video_segments(
    video_files: List[str],
    output_path: str,
    transition_duration: float = XFADE_TRANSITION_TIME,
    batch_size: int = CLIP_BATCH_SIZE,
    theme: str = None,
    lang_code: str = 'en'
):
    """合并多个视频片段
    Args:
        video_files: 要合并的视频文件列表
        output_path: 最终输出路径
        transition_duration: 转场时长
        batch_size: 每批处理的文件数
        theme: 主题名称
        lang_code: 语言代码
    """
    try:
        if not video_files:
            raise ValueError("没有输入文件")

        # 从 output_path 推断主题名称（如果未提供）
        if theme is None:
            theme = os.path.basename(os.path.dirname(os.path.dirname(output_path)))

        # 使用配置的 TEMP_DIR
        temp_dir = TEMP_DIR.format(theme=theme)
        # 确保临时目录路径一致，不附加语言代码
        os.makedirs(temp_dir, exist_ok=True)

        # 校验文件
        for vf in video_files:
            if not os.path.exists(vf):
                raise FileNotFoundError(f"文件不存在: {vf}")
            if os.path.getsize(vf) == 0:
                raise ValueError(f"文件为空: {vf}")

        # 初始化时长缓存
        duration_cache = VideoDurationCache()

        # 树形合并主循环
        current_files = video_files
        round_index = 1

        while len(current_files) > 1:
            logger.info(f"开始第 {round_index} 轮合并，当前文件数: {len(current_files)}")
            next_round_files = []

            # 按批次处理当前轮的文件
            for batch_index in range(0, len(current_files), batch_size):
                batch = current_files[batch_index:batch_index + batch_size]

                if len(batch) == 1:
                    next_round_files.append(batch[0])
                    continue

                # 生成临时文件名 (不带语言代码，确保路径一致性)
                batch_output = os.path.join(
                    temp_dir,
                    f"round_{round_index}_batch_{batch_index//batch_size}.mp4"
                )

                logger.info(f"处理第 {round_index} 轮第 {batch_index//batch_size + 1} 批，"
                          f"合并 {len(batch)} 个文件 -> {batch_output}")

                # 构建 FFmpeg 命令
                command = ["ffmpeg", "-y"] + FFMPEG_BASE_PARAMS

                # 添加输入文件，检查每个文件
                for f in batch:
                    logger.debug(f"添加输入文件: {f} (存在: {os.path.exists(f)}, 大小: {os.path.getsize(f) if os.path.exists(f) else 0})")
                    command += ["-i", f]

                filter_complex = []

                # 1. 首先为所有输入视频和音频流设置基本处理
                for i in range(len(batch)):
                    # 视频流处理
                    filter_complex.append(
                        f"[{i}:v]settb=1/{VIDEO_FPS},fps={VIDEO_FPS},setpts=PTS-STARTPTS[vid{i}_fixed]"
                    )
                    # 音频流处理
                    filter_complex.append(
                        f"[{i}:a]aformat=sample_fmts=fltp:sample_rates=44100:"
                        f"channel_layouts=stereo,asetpts=PTS-STARTPTS[au{i}]"
                    )

                # 2. 视频过渡处理（彻底修复的逻辑）
                cumulative_duration = Decimal('0')
                last_tag = "vid0_fixed"
                last_audio_tag = "au0"

                for i in range(len(batch) - 1):
                    cur_dur = duration_cache.get_duration(batch[i])
                    actual_trans = min(cur_dur, Decimal(str(transition_duration)))
                    actual_trans = max(Decimal('0'), actual_trans)

                    # 1️⃣ 计算正确的偏移位置
                    offset = cumulative_duration + cur_dur - actual_trans

                    # 记录调试信息
                    logger.debug(f"xfade#{i}: cur={cur_dur}s, offset={offset}s, cumulative={cumulative_duration}s")

                    # 2️⃣ 生成视频 xfade
                    filter_complex.append(
                        f"[{last_tag}][vid{i+1}_fixed]xfade=transition=fade:"
                        f"duration={format_float(actual_trans, 6)}:"
                        f"offset={format_float(offset, 6)}[v_tmp{i}]"
                    )
                    # 重要：归零PTS，防止下一轮计算错误
                    filter_complex.append(f"[v_tmp{i}]setpts=PTS-STARTPTS[v{i}]")
                    last_tag = f"v{i}"

                    # 3️⃣ 生成音频 acrossfade（同理）
                    filter_complex.append(
                        f"[{last_audio_tag}][au{i+1}]acrossfade=d={format_float(actual_trans, 6)}:"
                        f"c1=tri:c2=tri[a_tmp{i}]"
                    )
                    filter_complex.append(f"[a_tmp{i}]asetpts=PTS-STARTPTS[au{i}]")
                    last_audio_tag = f"au{i}"

                    # 4️⃣ 更新累计时长 - 只加"净增长"
                    cumulative_duration += cur_dur - actual_trans

                # 5. 完成命令构建 - 确保映射的标签与滤镜链中的最终标签匹配
                filter_str = ";".join(filter_complex)

                # 最后一个转场的索引是 len(batch) - 2
                last_index = len(batch) - 2
                if last_index >= 0:
                    # 如果有转场操作，使用最后一次转场的输出
                    command += [
                        "-filter_complex", filter_str,
                        "-map", f"[v{last_index}]",      # 映射最终的视频流标签
                        "-map", f"[au{last_index}]"      # 映射最终的音频流标签
                    ] + VIDEO_ENCODE_PARAMS + AUDIO_ENCODE_PARAMS + [batch_output]
                else:
                    # 如果只有一个片段（不应该走到这里）
                    command += [
                        "-filter_complex", filter_str,
                        "-map", "[vid0_fixed]",
                        "-map", "[au0]"
                    ] + VIDEO_ENCODE_PARAMS + AUDIO_ENCODE_PARAMS + [batch_output]

                logger.debug("FFmpeg 命令：\n" + " ".join(command))

                # 添加详细日志，帮助排查问题
                logger.info(f"开始执行FFmpeg命令，合并 {len(batch)} 个片段...")
                try:
                    result = subprocess.run(command, check=True, capture_output=True, text=True)
                    if result.stderr:
                        logger.debug(f"FFmpeg stderr: {result.stderr}")
                except subprocess.CalledProcessError as e:
                    logger.error(f"FFmpeg 错误: {e.stderr}")
                    raise

                # 验证输出
                if not os.path.exists(batch_output):
                    raise FileNotFoundError(f"合并后文件未生成: {batch_output}")
                if os.path.getsize(batch_output) == 0:
                    raise ValueError(f"合并后文件为空: {batch_output}")

                logger.info(f"成功合并批次文件: {batch_output}")
                next_round_files.append(batch_output)

                # 清理本轮不再需要的临时文件
                if round_index > 1 and GENERATE_VIDEO_DEBUG is False:  # 添加调试变量判断
                    for f in batch:
                        if f.startswith(temp_dir):  # 只删除临时目录中的文件
                            try:
                                os.remove(f)
                                logger.debug(f"已删除临时文件: {f}")
                            except OSError as e:
                                logger.warning(f"删除临时文件失败 {f}: {e}")

            # 更新下一轮要处理的文件列表
            current_files = next_round_files
            round_index += 1

        # 最终文件时长修正（在现有代码结尾处）
        if len(current_files) == 1:
            final_temp_file = current_files[0]
            if os.path.exists(final_temp_file):
                # 计算正确的总时长
                total_duration = sum(duration_cache.get_duration(f) for f in video_files)
                corrected_duration = total_duration - (len(video_files) - 1) * Decimal(str(transition_duration))

                # 创建临时修正文件
                fixed_output = f"{output_path}.fixed.mp4"

                # 修剪到正确时长
                subprocess.run([
                    "ffmpeg", "-y", "-hide_banner", "-loglevel", "error",
                    "-i", final_temp_file,
                    "-c:v", "copy", "-c:a", "copy",
                    "-t", str(float(corrected_duration)),
                    fixed_output
                ], check=True)

                # 替换原文件
                os.replace(fixed_output, output_path)
                logger.info(f"已修正最终文件时长: {corrected_duration}秒")
            else:
                raise FileNotFoundError(f"找不到最终合并文件: {final_temp_file}")

        # 清理临时文件夹
        if not GENERATE_VIDEO_DEBUG:  # 添加调试变量判断
            try:
                for f in os.listdir(temp_dir):
                    os.remove(os.path.join(temp_dir, f))
                os.rmdir(temp_dir)
                logger.debug(f"已清理临时目录: {temp_dir}")
            except OSError as e:
                logger.warning(f"清理临时目录失败: {e}")

        # 最后添加一个计算正确时长的部分
        # 正确的总时长 = 所有片段时长 - (片段数-1) × 转场时长
        total_duration = sum(duration_cache.get_duration(f) for f in video_files)
        corrected_duration = total_duration - (len(video_files) - 1) * Decimal(str(transition_duration))

        logger.info(f"所有片段原始总时长: {total_duration}秒")
        logger.info(f"考虑 {len(video_files)-1} 个转场(每个 {transition_duration}秒)后的正确时长: {corrected_duration}秒")

    except Exception as e:
        logger.error(f"合并视频段落出错: {str(e)}")
        raise

# ============== 生成最终视频入口 ==============

def generate_final_video(
    segments: List[Dict[str, Any]],
    output_path: str,
    resolution: tuple = VIDEO_RESOLUTION,
    fps: int = VIDEO_FPS,
    transition_duration: float = XFADE_TRANSITION_TIME,
    transition_type: str = DEFAULT_TRANSITION_TYPE,
    output_dir: str = None,
    theme: str = None,
    lang_code: str = 'en',
    use_depthflow: bool = False,  # 新增参数：是否使用 DepthFlow
    use_crop: bool = False,       # 新增参数：是否使用 cover+crop 预处理
    use_upscale: bool = False     # 新增参数：是否使用图片放大
):
    """
    1. 先逐段生成带音频的短视频文件(可能是图片+音频，也可能是黑场+音频)；
    2. 再按照顺序合并这些短视频，做 xfade(视频) + acrossfade(音频)。
    """
    try:
        if not segments:
            raise ValueError("segments 列表为空")

        # 如果未提供主题，从输出路径推断
        if theme is None:
            theme = os.path.basename(os.path.dirname(os.path.dirname(output_path)))

        # 保证按照 segment_id 排序
        segments.sort(key=lambda x: x.get('segment_id', 0))

        if not output_dir:
            output_dir = os.path.dirname(output_path)
        os.makedirs(output_dir, exist_ok=True)

        # 逐段处理生成临时文件
        processed_segments = OrderedDict()
        total_segments = len(segments)
        for i, seg in enumerate(segments, 1):
            logger.info(f"处理段落 {i}/{total_segments} (segment_id={seg['segment_id']})")
            seg_file = process_segment(
                segment=seg,
                output_dir=output_dir,
                resolution=resolution,
                fps=fps,
                segment_index=i,
                lang_code=lang_code,
                use_depthflow=use_depthflow,  # 传递 DepthFlow 参数
                use_crop=use_crop,           # 传递 crop 参数
                use_upscale=use_upscale      # 传递 upscale 参数
            )
            processed_segments[i] = seg_file

        # 所有片段文件按顺序放入列表
        segment_files = list(processed_segments.values())

        # 验证所有文件存在
        for i, f in enumerate(segment_files):
            if not os.path.exists(f):
                logger.error(f"段落 {i+1} 文件不存在: {f}")
            elif os.path.getsize(f) == 0:
                logger.error(f"段落 {i+1} 文件为空: {f}")

        # 合并所有片段，确保传入正确的语言参数
        logger.info(f"开始分批合并所有片段... (总共 {len(segment_files)} 个文件, 语言代码: {lang_code})")
        merge_video_segments(
            video_files=segment_files,
            output_path=output_path,
            transition_duration=transition_duration,
            batch_size=CLIP_BATCH_SIZE,
            theme=theme,
            lang_code=lang_code
        )
        logger.info(f"合并完成: {output_path}")
    except Exception as e:
        logger.error(f"生成最终视频出错: {e}")
        raise

def generate_video_from_clip(
    video_path: str,
    output_path: str,
    audio_path: str,
    start_time: float = None,
    end_time: float = None,
    resolution: tuple = VIDEO_RESOLUTION,
    fps: int = VIDEO_FPS
) -> str:
    """从视频片段生成新的视频（可选裁剪时间段）"""
    try:
        if not os.path.exists(video_path):
            raise FileNotFoundError(f"视频文件不存在: {video_path}")
        if not os.path.exists(audio_path):
            raise FileNotFoundError(f"音频文件不存在: {audio_path}")

        # 获取音频的准确时长
        actual_audio_duration = get_video_duration(audio_path) # 使用 get_video_duration 也适用于纯音频

        # 计算视频素材的截取时长
        src_video_duration = get_video_duration(video_path)
        video_start_time = Decimal(str(start_time)) if start_time is not None else Decimal('0')
        video_end_time = Decimal(str(end_time)) if end_time is not None else src_video_duration

        if video_end_time > src_video_duration:
            logger.warning(f"视频结束时间 {video_end_time}s 超过源视频时长 {src_video_duration}s，自动调整")
            video_end_time = src_video_duration

        requested_video_segment_duration = video_end_time - video_start_time
        if requested_video_segment_duration <= Decimal('0.1'): # 保持原有校验
            raise ValueError(f"无效视频时间段：start={video_start_time} end={video_end_time} (源视频时长={src_video_duration}s)")

        # 关键：确定最终片段时长，取视频截取部分和音频时长的较小者
        final_clip_duration = min(requested_video_segment_duration, actual_audio_duration)

        command = ["ffmpeg", "-y"] + FFMPEG_BASE_PARAMS
        command.extend(["-ss", str(video_start_time)]) # 从视频的 video_start_time 开始
        command.extend(["-t", str(final_clip_duration)]) # 截取 final_clip_duration 长度的视频
        command.extend(["-i", video_path])    # 输入0 (视频)

        command.extend(["-i", audio_path])    # 输入1 (音频)

        filter_complex = [
            f"[0:v]format=yuv420p,scale={resolution[0]}:{resolution[1]},fps={fps},setpts=PTS-STARTPTS[v]",
            # 确保音频流也被正确处理并重新计时，以匹配视频流的行为
            f"[1:a]aformat=sample_fmts=fltp:sample_rates=44100:channel_layouts=stereo,asetpts=PTS-STARTPTS[a_processed]"
        ]

        command.extend([
            "-filter_complex", ";".join(filter_complex),
            "-map", "[v]",
            "-map", "[a_processed]", # 使用处理后的音频流
            "-shortest"
        ])

        # 添加编码参数
        command.extend(VIDEO_ENCODE_PARAMS + AUDIO_ENCODE_PARAMS + [output_path])

        # 执行命令
        logger.debug("FFmpeg 命令 (generate_video_from_clip)：\\n" + " ".join(command))
        result = subprocess.run(command, check=True, capture_output=True, text=True)
        if result.stderr:
            logger.debug(f"FFmpeg stderr: {result.stderr}")

        # 验证输出文件
        verify_media_file(output_path)
        return output_path

    except Exception as e:
        logger.error(f"从视频片段生成视频失败 ({output_path}): {e}")
        # 清理可能部分生成的文件
        if os.path.exists(output_path):
            try:
                os.remove(output_path)
            except:
                pass
        raise

def main():
    parser = argparse.ArgumentParser(description='生成最终视频')
    parser.add_argument('--input', required=True, help='输入JSON文件路径')
    parser.add_argument('--theme', required=True, help='主题名称')
    parser.add_argument('--width', type=int, default=VIDEO_RESOLUTION[0])
    parser.add_argument('--height', type=int, default=VIDEO_RESOLUTION[1])
    parser.add_argument('--fps', type=int, default=VIDEO_FPS)
    parser.add_argument('--transition_duration', type=float, default=XFADE_TRANSITION_TIME)
    parser.add_argument('--transition_type', default=DEFAULT_TRANSITION_TYPE)
    parser.add_argument('--lang', type=str, default="Chinese",
                       help="Language for TTS (default: Chinese, options: English, English-Story, Japanese, Chinese)")
    parser.add_argument('--depthflow', action='store_true',
                       help="使用 DepthFlow 生成更好的图片动画效果")
    parser.add_argument('--crop', action='store_true',
                       help="Use cover+crop preprocessing for all images")
    parser.add_argument('--upscale', action='store_true',
                       help="对所有图片进行4x放大处理后再生成视频")

    args = parser.parse_args()

    try:
        # 1. 加载JSON
        segments = load_json_file(args.input)

        # 2. 输出目录
        theme = args.theme
        base_output_dir = CLIP_DIR.format(theme=theme)

        # 根据语言决定是否添加语言代码后缀
        lang_code = get_language_code(args.lang)

        # 修改: 保持一致的输出目录逻辑
        output_dir = base_output_dir
        if args.lang.lower() != 'english':
            output_dir = f"{base_output_dir}_{lang_code}"
        os.makedirs(output_dir, exist_ok=True)

        # 3. 最终输出文件名
        json_dirname = os.path.dirname(args.input)
        json_basename = os.path.basename(args.input)

        # 处理所有可能的输入文件命名模式
        output_name = (json_basename
                      .replace('_audio_results.json', '_video.mp4')
                      .replace('_audio_result.json', '_video.mp4')
                      .replace('_audio.json', '_video.mp4'))

        # 添加安全检查，确保扩展名正确
        if output_name.endswith('.json'):
            output_name = output_name.rsplit('.', 1)[0] + '.mp4'

        output_path = os.path.join(json_dirname, output_name)

        # 4. 生成最终视频，确保传递正确的语言代码和DepthFlow选项
        generate_final_video(
            segments=segments,
            output_path=output_path,
            resolution=(args.width, args.height),
            fps=args.fps,
            transition_duration=args.transition_duration,
            transition_type=args.transition_type,
            output_dir=output_dir,
            theme=theme,
            lang_code=lang_code,
            use_depthflow=args.depthflow,  # 传递 DepthFlow 选项
            use_crop=args.crop,            # 传递 crop 选项
            use_upscale=args.upscale       # 传递 upscale 选项
        )

        logger.info("视频生成完成！")

    except Exception as e:
        logger.error(f"处理失败: {e}")
        raise

if __name__ == "__main__":
    main()